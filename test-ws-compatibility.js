/**
 * WebSocket 兼容性测试脚本
 * 用于验证 ws 7.5.10 在 Node.js v14.21.3 环境下的功能
 */

const WebSocket = require('ws');
const http = require('http');

console.log('Node.js 版本:', process.version);
console.log('开始 WebSocket 兼容性测试...\n');

// 创建 HTTP 服务器
const server = http.createServer();
const wss = new WebSocket.Server({ server });

let testResults = {
  serverCreation: false,
  clientConnection: false,
  messageTransmission: false,
  errorHandling: false,
  connectionClose: false
};

// 测试服务器创建
try {
  console.log('✓ WebSocket 服务器创建成功');
  testResults.serverCreation = true;
} catch (error) {
  console.error('✗ WebSocket 服务器创建失败:', error.message);
}

// 服务器连接处理
wss.on('connection', function connection(ws, req) {
  console.log('✓ 客户端连接成功');
  testResults.clientConnection = true;

  // 测试消息接收
  ws.on('message', function message(data) {
    console.log('✓ 收到消息:', data.toString());
    testResults.messageTransmission = true;
    
    // 回显消息
    ws.send(`服务器回复: ${data}`);
  });

  // 测试错误处理
  ws.on('error', function error(err) {
    console.log('✓ 错误处理正常:', err.message);
    testResults.errorHandling = true;
  });

  // 测试连接关闭
  ws.on('close', function close() {
    console.log('✓ 连接关闭处理正常');
    testResults.connectionClose = true;
    
    // 显示测试结果
    setTimeout(showTestResults, 100);
  });

  // 发送欢迎消息
  ws.send('欢迎连接到 WebSocket 服务器！');
});

// 启动服务器
const PORT = 8080;
server.listen(PORT, function() {
  console.log(`WebSocket 服务器启动在端口 ${PORT}`);
  
  // 创建测试客户端
  setTimeout(createTestClient, 1000);
});

function createTestClient() {
  console.log('\n开始客户端测试...');
  
  const client = new WebSocket(`ws://localhost:${PORT}`);
  
  client.on('open', function open() {
    console.log('✓ 客户端连接建立');
    
    // 发送测试消息
    client.send('这是一条测试消息');
    
    // 2秒后关闭连接
    setTimeout(() => {
      client.close();
    }, 2000);
  });
  
  client.on('message', function message(data) {
    console.log('✓ 客户端收到消息:', data.toString());
  });
  
  client.on('error', function error(err) {
    console.error('✗ 客户端错误:', err.message);
  });
  
  client.on('close', function close() {
    console.log('✓ 客户端连接关闭');
    
    // 关闭服务器
    server.close();
  });
}

function showTestResults() {
  console.log('\n=== WebSocket 兼容性测试结果 ===');
  console.log('服务器创建:', testResults.serverCreation ? '✓ 通过' : '✗ 失败');
  console.log('客户端连接:', testResults.clientConnection ? '✓ 通过' : '✗ 失败');
  console.log('消息传输:', testResults.messageTransmission ? '✓ 通过' : '✗ 失败');
  console.log('错误处理:', testResults.errorHandling ? '✓ 通过' : '⚠ 未测试');
  console.log('连接关闭:', testResults.connectionClose ? '✓ 通过' : '✗ 失败');
  
  const passedTests = Object.values(testResults).filter(result => result === true).length;
  const totalTests = Object.keys(testResults).length;
  
  console.log(`\n总体结果: ${passedTests}/${totalTests} 项测试通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！ws 7.5.10 在当前环境下工作正常。');
  } else {
    console.log('⚠️  部分测试未通过，请检查配置或环境。');
  }
  
  process.exit(0);
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('✗ 未捕获的异常:', error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('✗ 未处理的 Promise 拒绝:', reason);
  process.exit(1);
});
