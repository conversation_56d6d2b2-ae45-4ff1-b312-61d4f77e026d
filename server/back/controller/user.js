const Response = require('../response/index')
const UserService = require('../service/user')

const UserController = {
	login: async (res, body) => {
		const R = await UserService.login(body)
		Response.result(res, R)
	},
	findInfoById: async (res, params) => {
		const R = await UserService.findInfoById(params.id)
		Response.result(res, R)
	},
	register: async (res, body) => {
		const R = await UserService.register(body)
		Response.result(res, R)
	},
	resetPassword: async (res, body) => {
		const R = await UserService.resetPassword(body.id)
		Response.result(res, R)
	},
	updatePassword: async (res, body) => {
		const R = await UserService.updatePassword(body)
		Response.result(res, R)
	},
	update: async (res, body) => {
		const R = await UserService.update(body)
		Response.result(res, R)
	},
	delete: async (res, params) => {
		const R = await UserService.delete(params.id)
		Response.result(res, R)
	},
	pageList: async (res, body) => {
		const R = await UserService.pageList(body)
		Response.result(res, R)
	},
	getFaceDetectInfo: (res, body) => {
		Response.result(res, UserService.getFaceDetectInfo(body))
	},
	faceCompareManyByBase64: async (res, body) => {
		const R = await UserService.faceCompareManyByBase64(body)
		Response.result(res, R)
	},
	gestureLogin: async (res, body) => {
		const R = await UserService.gestureLogin(body)
		Response.result(res, R)
	},
	batchDelete: async (res, params) => {
		const R = await UserService.batchDelete(params.ids)
		Response.result(res, R)
	},
	fingerCompareManyByFingerFeature: async (res, body) => {
		const R = await UserService.fingerCompareManyByFingerFeature(body)
		Response.result(res, R)
	},
	userListImport: async (res, body, req) => {
		const boundary = req.headers['content-type'].split('; ')[1].replace('boundary=', '')
		const R = await UserService.userListImport(boundary, body)
		Response.result(res, R)
	},
	faceListImport: async (res, body, req) => {
		const boundary = req.headers['content-type'].split('; ')[1].replace('boundary=', '')
		const R = await UserService.faceListImport(boundary, body)
		Response.result(res, R)
	},
	synchronizePersonnel: async (res, body) => {
		const R = await UserService.synchronizePersonnel(body)
		Response.result(res, R)
	},
	synchronizePersonnel_old: async (res, body) => {
		const R = await UserService.synchronizePersonnel_old(body)
		Response.result(res, R)
	},
	allUserList: async (res) => {
		const R = await UserService.allUserList()
		Response.result(res, R)
	}
}

module.exports = UserController
