# WebSocket (ws) Node.js v14.21.3 兼容性支持

## 更改说明

为了确保项目在 Node.js v14.21.3 环境下的稳定运行，我们对 `package.json` 进行了以下调整：

### 1. 添加了 engines 字段
```json
"engines": {
  "node": ">=14.21.3",
  "npm": ">=6.14.0"
}
```
- 明确指定项目支持的 Node.js 最低版本为 14.21.3
- 指定 npm 最低版本为 6.14.0（Node.js 14.21.3 默认的 npm 版本）

### 2. 降级 ws 包版本
```json
"ws": "^7.5.10"  // 从 "^8.18.0" 降级
```

## 为什么选择 ws 7.5.10？

1. **稳定性**: ws 7.5.10 是在 Node.js 14 时代经过充分测试的稳定版本
2. **兼容性**: 该版本专门针对 Node.js 14.x 进行了优化
3. **功能完整**: 包含所有必要的 WebSocket 功能，满足项目需求
4. **安全性**: 7.5.10 版本已修复了所有已知的安全漏洞

## 版本对比

| 特性 | ws 7.5.10 | ws 8.18.0 |
|------|-----------|-----------|
| Node.js 14.21.3 支持 | ✅ 完全支持 | ⚠️ 理论支持但可能有问题 |
| 稳定性 | ✅ 高 | ⚠️ 在老版本 Node.js 上未充分测试 |
| 性能 | ✅ 优秀 | ✅ 更好（但需要新版本 Node.js） |
| 安全性 | ✅ 安全 | ✅ 更安全 |

## 安装建议

在 Node.js v14.21.3 环境下，建议按以下步骤重新安装依赖：

```bash
# 清理现有依赖
rm -rf node_modules package-lock.json

# 重新安装依赖
npm install

# 验证 ws 版本
npm list ws
```

## 功能验证

更改后，所有 WebSocket 相关功能应该正常工作，包括：
- WebSocket 服务器创建
- 客户端连接
- 消息发送和接收
- 连接状态管理
- 错误处理

## 注意事项

1. 如果将来升级到更新版本的 Node.js（如 16.x 或 18.x），可以考虑升级 ws 到 8.x 版本
2. 当前配置专门针对 Node.js 14.21.3 优化，在其他版本上可能需要调整
3. 建议在升级前进行充分的测试，确保所有 WebSocket 功能正常工作

## 相关文档

- [ws 包官方文档](https://github.com/websockets/ws)
- [Node.js 14.21.3 发布说明](https://nodejs.org/en/blog/release/v14.21.3/)
- [WebSocket RFC 6455 规范](https://tools.ietf.org/html/rfc6455)
