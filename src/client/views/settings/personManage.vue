<template>
	<div class="person-manage">
		<div class="ui-tabs-border">
			<div v-for="(item, idx) in tabList" :key="idx" :class="['tab', searchParams.userType === item.value && 'active']" @click="changeTab(item)">{{ item.label }}</div>
		</div>
		<div class="search-box">
			<div class="search-input">
				<ui-input v-keyboard placeholder="输入姓名或警号查询" v-model="searchParams.name"></ui-input>
				<div class="search-btn" @click="getList()">{{ $t(`人员管理.查询`) }}</div>
			</div>
			<div class="synchronous" @click="modalShow = true">{{ $t(`人员管理.同步人员信息`) }}</div>
		</div>
		<div class="card-box wrapper" ref="wrapper">
			<div class="content">
				<div :class="['card-item', item.select && 'selected', item.loginId == 'gly' && 'deleted']" v-for="(item, idx) in cardList" :key="idx" @click="item.loginId != 'gly' ? selectCard(item) : null">
					<div class="icon"></div>
					<div class="card-content">
						<img class="img" :src="item.faceImg || (item.userType === '3' ? personImg : policeImg)" alt="" />
						<div>
							<div class="name">{{ item.name }}{{ item.policeNumber && '(' + item.policeNumber + ')' }}</div>
							<div class="register-time">{{ $t(`人员管理.注册时间`) }}:{{ item.createTime }}</div>
						</div>
					</div>
					<div class="btn" @click.stop="toPage(item)">{{ $t(`人员管理.完善信息`) }}</div>
				</div>
			</div>
		</div>
		<div class="del-box">
			<div class="select-box" @click="toggleSelectAll()">
				<div :class="['icon', isAllSelected && 'selected']"></div>
				<div class="text">{{ $t(`人员管理.全选`) }}</div>
			</div>
			<div class="btn-box">
				<div class="text">
					{{ $t(`人员管理.已选`) }}
					<span>{{ selectedList.length }}</span>
					人
				</div>
				<div class="btn" :class="{ disabled: selectedList.length == 0 }" @click="selectedList.length > 0 ? delModal(true) : null">{{ $t(`人员管理.删除`) }}</div>
			</div>
		</div>
		<ui-modal v-model="modalShow" :title="$t(`人员管理.同步人员信息`)">
			<div class="ui-form-box">
				<div class="ui-form-item">
					<div class="label">{{ $t(`人员管理.是否主柜`) }}</div>
					<div class="value">
						<ui-radio v-model="radio" label="1">{{ $t(`人员管理.是`) }}</ui-radio>
						<ui-radio v-model="radio" label="2">{{ $t(`人员管理.否`) }}</ui-radio>
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">{{ $t(`人员管理.主柜IP`) }}</div>
					<div class="value">
						<ui-input v-keyboard v-model="submitForm.ip"></ui-input>
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">{{ $t(`人员管理.端口`) }}</div>
					<div class="value">
						<ui-input v-keyboard v-model="submitForm.port"></ui-input>
					</div>
				</div>
			</div>
			<div class="ui-modal-footer">
				<div class="btn" @click="submit()">{{ $t(`人员管理.同步`) }}</div>
			</div>
		</ui-modal>
		<ui-modal v-model="delModalShow" title="提示">
			<div class="modal-content">删除人员后将一并删除关联的数据？</div>
			<div class="ui-modal-footer">
				<div class="btn cancel-btn" @click="delModal(false)">取消</div>
				<div class="btn" @click="delSubmit()">确认</div>
			</div>
		</ui-modal>
	</div>
</template>

<script>
export default {
	name: 'personManage',
	data() {
		return {
			delModalShow: false,
			modalShow: false,
			tabValue: '',
			policeImg: require('../../assets/images/settings/police.png'),
			personImg: require('../../assets/images/settings/person.png'),
			tabList: [
				{ value: '', label: this.$t(`人员管理.全部`) },
				{ value: '1', label: this.$t(`人员管理.管理员`) },
				{ value: '2', label: this.$t(`人员管理.工作人员`) },
				{ value: '3', label: this.$t(`人员管理.非工作人员`) }
			],
			cardList: [],
			radio: '1',
			searchParams: {
				size: 99,
				page: 1,
				userType: '',
				name: '',
				associatedPoliceId: ''
			},
			total: 0,
			submitForm: {},
			tableUserData: []
		}
	},
	computed: {
		selectedList() {
			return this.cardList.filter((card) => card.select)
		},
		isAllSelected() {
			return this.cardList.filter((card) => card.loginId !== 'gly').every((card) => card.select)
		}
	},
	methods: {
		toPage(item) {
			this.$router.push({ path: '/improveInformation', query: { id: item.id } })
		},
		submit() {
			// const { ip, port } = this.submitForm
			// if (!ip || !port) {
			// 	return this.$baseTip.info('请填写正确地址')
			// }
			console.log(this.$http.userApi,'this.$http.userApi.this.$http.userApi.this.$http.userApi.')
			this.$http.userApi.synchronizePersonnel({ ip, port }).then((res) => {
				this.$baseTip.success('操作成功,请稍后尝试')
				this.modalShow = false
			})
		},
		toggleSelectAll() {
			const allSelected = this.isAllSelected
			this.cardList.forEach((card) => {
				card.loginId != 'gly' && this.$set(card, 'select', !allSelected)
			})
		},
		changeTab(item) {
			this.searchParams.userType = item.value
			this.getList()
		},
		selectCard(item) {
			this.$set(item, 'select', !item.select)
		},
		async delSubmit() {
			const res = await this.$http.webApi.getIscdsUserList()
			this.tableUserData = res.data
			for (let i = 0; i < this.tableUserData.length; i++) {
				for (let j = 0; j < this.selectedList.length; j++) {
					const user = this.tableUserData[i]
					const select = this.selectedList[j]
					if (user.associatedPoliceId == select.id) {
						return this.$baseTip.info('正在使用智能柜的涉案人员，所关联的民警不能删除！')
					}
				}
			}
			const ids = this.selectedList.map((item) => item.id).join(',')
			this.$http.webApi.userBatchDelete({ ids }).then(() => {
				this.delModal(false)
				this.$baseTip.success(this.$t(`人员管理.删除成功`))
				this.getList()
			})
		},
		delModal(flag) {
			this.delModalShow = flag
		},
		getList() {
			this.searchParams.page = 1
			this.cardList = []
			this.$http.webApi.userPageList(this.searchParams).then((res) => {
				const { data, total } = res.data
				this.cardList = data || []
				this.total = total
			})
		}
	},
	created() {
		this.searchParams.associatedPoliceId = this.$store.state.user.userInfo.userType == 2 ? this.$store.state.user.userInfo.id : ''
		this.getList()
	}
}
</script>
<style lang="less">
.person-manage {
	display: flex;
	flex-direction: column;
	.search-box {
		height: 96px;
		margin-top: 40px;
		display: flex;
		justify-content: space-between;
		padding: 0 40px;
		.search-input {
			width: 679px;
			padding-right: 147px;
			position: relative;
			.search-btn {
				position: absolute;
				top: 0;
				right: 0;
				width: 154px;
				height: 96px;
				background: #669eff;
				border-radius: 8px;
				font-size: 40px;
				color: #ffffff;
				line-height: 96px;
				text-align: center;
			}
		}
		.synchronous {
			width: 280px;
			height: 96px;
			background: #669eff;
			border-radius: 8px;
			font-size: 40px;
			color: #ffffff;
			line-height: 96px;
			text-align: center;
		}
	}
	.card-box {
		flex: 1;
		max-height: 1260px;
		position: relative;
		overflow-y: auto;
		&::-webkit-scrollbar {
			width: 0px;
		}
		.content {
			padding: 40px;
			// min-height: 1270px;
		}
		.card-item {
			height: 200px;
			background: #ffffff;
			box-shadow: 0px 10px 10px 1px rgba(115, 157, 229, 0.15), inset 0px 3px 3px 1px rgba(48, 116, 218, 0.1);
			border-radius: 8px;
			margin-bottom: 32px;
			display: flex;
			align-items: center;
			padding-left: 32px;
			padding-right: 40px;
			&.deleted {
				.icon {
					width: 54px;
					height: 54px;
					background: #ccc;
					border-radius: 50%;
					border: none;
				}
			}
			.icon {
				width: 54px;
				height: 54px;
				border: 2px solid #20dbe9;
				border-radius: 50%;
			}
			.card-content {
				flex: 1;
				display: flex;
				padding-left: 36px;
				.img {
					width: 132px;
					height: 136px;
					background: #a1cdff;
					border-radius: 8px;
					margin-right: 46px;
				}
				.name {
					font-size: 36px;
					color: #2b3346;
					line-height: 74px;
					font-weight: bold;
				}
				.register-time {
					font-weight: 400;
					font-size: 28px;
					color: #5f709a;
					line-height: 62px;
				}
			}
			.btn {
				width: 187px;
				height: 73px;
				background: #ffffff;
				border-radius: 6px;
				border: 3px solid #337eff;
				font-size: 32px;
				color: #337eff;
				line-height: 73px;
				text-align: center;
			}
			&.selected {
				border: 3px solid #20dbe9;
				.icon {
					background-image: url('../../assets/images/settings/icon_selected.png');
					background-size: cover;
					background-position: center;
					background-repeat: no-repeat;
				}
			}
		}
	}
	.del-box {
		position: fixed;
		bottom: 0;
		width: 1080px;
		height: 160px;
		background: #ffffff;
		padding-left: 75px;
		padding-right: 80px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.select-box {
			display: flex;
			align-items: center;
			.icon {
				width: 54px;
				height: 54px;
				border: 2px solid #20dbe9;
				border-radius: 50%;
				&.selected {
					background-image: url('../../assets/images/settings/icon_selected.png');
					background-size: cover;
					background-position: center;
					background-repeat: no-repeat;
				}
			}
			.text {
				font-size: 36px;
				color: #333333;
				margin-left: 28px;
			}
		}
		.btn-box {
			display: flex;
			align-items: center;
			.text {
				font-size: 26px;
				color: #3d4e66;
				margin-right: 32px;
				span {
					color: #337eff;
				}
			}
			.btn {
				width: 320px;
				height: 96px;
				background: #2b5fda;
				border-radius: 8px;
				font-size: 36px;
				color: #ffffff;
				line-height: 96px;
				text-align: center;
				&.disabled {
					background: rgba(43, 95, 218, 0.4);
				}
			}
		}
	}
}
.modal-content {
	height: 200px;
	margin-bottom: 44px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	font-size: 40px;
}
</style>
