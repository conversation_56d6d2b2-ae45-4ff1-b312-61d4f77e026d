const uac = '/api'
import request from '@/web/libs/request'
const userApi = {
	/**
	 * 获取登录信息
	 * @returns Promise
	 */
	login(data) {
		return request({
			url: `${uac}/user/login`,
			method: 'post',
			data
		})
	},
	/**
	 * 获取注册信息
	 * @returns Promise
	 */
	register(data) {
		return request({
			url: `${uac}/user/register`,
			method: 'post',
			data
		})
	},
	pageList(data) {
		return request({
			url: `${uac}/user/pageList`,
			method: 'post',
			data
		})
	},
	/**
	 * 重置密码
	 * @param {string} id 用户id
	 */
	resetPassword(id) {
		return request({
			url: `${uac}/user/resetPassword`,
			method: 'post',
			data: {
				id
			}
		})
	},
	/**
	 * 更新密码
	 * @param {Object} data
	 * @param {Object} data.id 用户id
	 * @param {Object} data.oldPassword 旧密码
	 * @param {Object} data.newPassword 新密码
	 * @param {Object} data.confirmNewPassword 确认新密码
	 * @returns Promise
	 */
	updatePassword(data) {
		return request({
			url: `${uac}/user/updatePassword`,
			method: 'post',
			data
		})
	},
	/**
	 * 更新信息
	 * @returns Promise
	 */
	update(data) {
		return request({
			url: `${uac}/user/update`,
			method: 'post',
			data
		})
	},
	/**
	 * 删除信息
	 * @returns Promise
	 */
	delete(params) {
		return request({
			url: `${uac}/user/delete`,
			method: 'delete',
			params
		})
	},
	/**
	 * 获取个人信息
	 * @returns Promise
	 */
	findInfoById(params) {
		return request({
			url: `${uac}/user/findInfoById`,
			method: 'get',
			params
		})
	},
	/**
	 * 人脸抠图
	 * @param {Object} data
	 * @returns Promise
	 */
	getFaceDetectInfo(data) {
		console.log(data,'getFaceDetectInfo')
		return request({
			url: `${uac}/user/getFaceDetectInfo`,
			method: 'post',
			data
		})
	},
	//用户导入
	userListImport(data, headers) {
		return request({
			url: `${uac}/user/userListImport`,
			method: 'post',
			headers: { 'Content-Type': 'multipart/form-data', ...headers },
			data
		})
	},
	faceListImport(data, headers) {
		return request({
			url: `${uac}/user/faceListImport`,
			method: 'post',
			headers: { 'Content-Type': 'multipart/form-data', ...headers },
			data
		})
	},
	synchronizePersonnel(params, headers) {
		return request({
			url: `/ptm-com/acp/pm/cnpFace/wpg/list`,
			method: 'get',
			headers: { 'Content-Type': 'multipart/form-data', ...headers },
			params
		})
	},
}

export default userApi
