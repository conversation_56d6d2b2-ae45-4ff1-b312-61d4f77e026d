<template>
    <div class="cabinet-main">
        <Carousel v-model="value" class="cabinet-arousel" dots="none" arrow="never">
            <CarouselItem v-for="item in cabinetArr" :key="item.sideCode">
                <div class="cabinet">
                    <div class="cabinet-body">
                        <template v-for="(item1, index) in item.positionArr">
                            <div :key="index * 999" class="col" :style="{ width: 100 / item.positionArr.length + '%' }">
                                <div v-for="(item2, index2) in item1.row" :key="index2 + 'row'" :class="cabinetStyle(item2)" :style="{ flex: ' 0 ' + item2.flexBasis * 100 + '%' }">
                                    <div :class="[item2.num == choosedCabinet.num ? 'active' : '']" @click="handleChooseCabinet(item2)">{{ item2.num != '000' ? item2.num : '' }}</div>
                                </div>
                            </div>
                            <div v-show="index != item.positionArr.length - 1" :key="(index + 1) * 888" class="col-line"></div>
                        </template>
                    </div>
                    <div class="status">
                        <span class="txt">库存状态：</span>
                        <span class="dot kx">
                            <i></i>
                            可用（{{ item.freeCount }}）
                        </span>
                        <span class="dot zy">
                            <i></i>
                            不可用（{{ item.assignCount }}）
                        </span>
                        <span class="dot gz">
                            <i></i>
                            故障（{{ item.faultCount }}）
                        </span>
                    </div>
                </div>
            </CarouselItem>
        </Carousel>
        <div v-if="gh_list.length <= 1" class="carousel-dot">
            <span v-for="(item, i) in cabinetArr" :key="item.sideCode" :class="{ active: value == i }" class="dot" @click="handleDot(i)">{{ item.sideCode }}</span>
        </div>
        <Carousel v-else class="dot-Carousel" dots="none" arrow="never">
            <CarouselItem v-for="(list, k) in gh_list" :key="k">
                <div class="carousel-dot">
                    <span v-for="(item, i) in list" :key="item.sideCode" :class="{ active: value == (k * 10 ) + i }" class="dot" @click="handleDot((k * 10 ) + i)">{{ item.sideCode }}</span>
                </div>
            </CarouselItem>
        </Carousel>

    </div>
</template>

<script>
const serverConfig = JSON.parse(localStorage.getItem('serverConfig') || '{}')
const ghs = ['A', 'B', 'C', 'D', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'Y', 'Z', 'A', 'B', 'C', 'D', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'Y', 'Z']
export default {
    name: 'CabinetMain',
    props: {
        currentCabinetNum: {
            require: true,
            type: String,
            default: ''
        },
        openedCabinetNum: {
            require: true,
            type: Array,
            default: () => []
        },
        cabinetDetail: {
            require: true,
            type: Array,
            default: () => []
        },
        choosedCabinet: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            ghs,
            value: 0,
            scrollIdx: 0,
            cabinetArr: [],
            titleSpan: 0,
            gh_list: []
        }
    },
    created() {
        this.init()
    },
    mounted() {
    },
    methods: {
        // 计算滚动组
        cabinetList() {
            const list = []
            for (let i = 0; i < Math.ceil(this.cabinetArr.length / 10); i++) {
                const _list = []
                for (let k = 0; k < 10; k++) {
                    const idx = (i * 10) + k
                    this.cabinetArr[idx] && _list.push(this.cabinetArr[idx])
                }
                list.push(_list)
            }
            this.gh_list = list
        },
        init() {
            this.cabinetArr = []
            this.cabinetDetail.map((item) => {
                const obj = {
                    sideCode: item.sideCode,
                    sideId: item.sideId,
                    positionArr: [],
                    freeCount: 0,
                    assignCount: 0,
                    faultCount: 0
                }
                obj.positionArr = this.dealCabinetDetail(item)
                console.log('obj.positionArr', obj.positionArr)
                obj.positionArr.forEach((position) => {
                    obj.freeCount = obj.freeCount + position.row.filter((item) => item.status == 'free' && item.num != '000').length
                    obj.assignCount = obj.assignCount + position.row.filter((item) => item.status == 'assign' && item.num != '000').length
                    obj.faultCount = obj.faultCount + position.row.filter((item) => item.status == 'fault' && item.num != '000').length
                })
                this.cabinetArr.push(obj)
                this.cabinetList()
            })
        },
        dealCabinetDetail(data) {
            const getCabinetOfWp = this.$store.getters.getCabinetOfWp
            const histroy = Object.keys(getCabinetOfWp)
            const colData = []
            data.data.map((item) => {
                // this.openedCabinetNum.indexOf(item.num) > -1 ? (item.open = true) : (item.open = false)
                if (colData.indexOf(item.colPos) == -1) colData.push(item.colPos)

                // 历史暂存占用柜子
                const his_item = histroy.find((his_item) => his_item == item.num)
                // 占用柜子遍历
                const ass_item = data.assign.find((ass_item) => ass_item.gh == item.num)
                // 错误柜子遍历
                const fault_item = data.fault.find((fault_item) => fault_item.gh == item.num)
                // 匹配柜子状态- 可用：0、不可用：1、2
                if (item.isFull == '1' || item.isForbidden == '1') {
                    item.status = 'assign'
                }
                if (ass_item) {
                    item.status = 'assign'
                    item.count = ass_item.count
                }
                if (fault_item) {
                    item.status = 'fault'
                }
                if (his_item) {
                    item.status = 'assign'
                }
                if (!ass_item && !fault_item && !his_item && item.isFull == '0' && item.isForbidden == '0') {
                    item.status = 'free'
                }
            })
            colData.sort((a, b) => a - b)
            const posData = []

            colData.map((col) => {
                const cabinetData = {
                    col
                }
                cabinetData.row = data.data.filter((item) => item.colPos == col)

                const totalMerges = cabinetData.row.reduce((newVal, item) => {
                    return Number(newVal) + Number(item.merge)
                }, 0)
                cabinetData.row.forEach((item) => {
                    item.flexBasis = item.merge / totalMerges
                    item.sideId = data.sideId
                    item.cabinetCode = serverConfig.cabinetCode
                })
                posData.push(cabinetData)
            })
            this.titleSpan = colData.length
            return posData
        },
        handleDot(i) {
            this.value = i
        },
        handleChooseCabinet(cabinet) {
            if (cabinet.num == '000') return
            if (['assign', 'fault'].includes(cabinet.status)) return this.$GxxMessage({ message: `该柜子已${cabinet.status == 'fault' ? '故障' : '占用'}`, type: 'warning'})
            this.$emit('choosed', cabinet)
        },
        // 柜格样式组装
        cabinetStyle(item2) {
            const style = ['row', item2.status]
            if (item2.num === '000') {
                style.push('screen')
            }
            if (item2.open) {
                style.push('active')
            }
            if (item2.num == this.currentCabinetNum) {
                style.push('activeAnimation')
            }
            return style
        }
    }
}
</script>

<style lang="less" scoped>
@assets: '../../../../assets/images';
.cabinet-main {
	width: 100%;
	height: auto;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: center;
	margin: 20px auto;
	.carousel-dot {
		display: flex;
		justify-content: center;
		height: 100px;
        max-width: 80vw;
        flex-wrap: wrap;
        align-items: center;
		.dot {
			display: inline-block;
			width: 48px;
			height: 48px;
			border-radius: 48px;
			background-color: #cce6ff;
			color: #5a90ff;
			font-size: 26px;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 12px;
			cursor: pointer;
			transition: all 0.5s;
            flex-shrink: 0;
			&.active {
				width: 64px;
				height: 64px;
				background-color: #337eff;
				color: #fff;
				box-shadow: 0px 4px 8px 1px rgba(51, 126, 255, 0.5);
			}
		}
	}
    .dot-Carousel {
        display: inline-block;
		height: 100%;
		width: calc(100% - 180px);
        margin-top: 30px;
        // overflow: hidden;
        /deep/.ivu-carousel-list {
            height: 92px;
            width: 100%;
            overflow: hidden;
        }
        .ivu-carousel-track {
            width: 100%;
            height: 100%;
        }
        /deep/.ivu-carousel-arrow {
            display: inline-block;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgb(26 108 202 / 11%);;
            &:hover {
                background-color: azure;
            }
            // &.right {
            //     right: -80px
            // }
            // &.left {
            //     left: -80px
            // }
            .ivu-icon {
                font-size: 28px;
                color: #5a90ff;
            }
        }
    }
	.cabinet-arousel {
		display: inline-block;
		height: 100%;
		width: calc(100% - 185px);
	}
	.cabinet {
		width: 100%;
		height: 1050px;
		background-image: linear-gradient(360deg, #5a90ff 0%, #4ce7ff 100%);
		// box-shadow: 0px 20px 18px 1px rgba(51,126,255,0.4);
		border-radius: 32px;
		padding: 40px;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		.title {
			width: 100%;
			height: 60px;
			background: #4d5f80;
			margin-bottom: 4px;
			color: #fff;
			font-size: 28px;
			line-height: 60px;
			text-align: center;
		}
		.cabinet-body {
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: space-between;
			margin: 0 auto;
			.col-line {
				width: 88px;
				border-radius: 4px;
				height: 100%;
				background-color: #99d5ff;
				margin: 0 16px;
			}
			.col {
				height: 100%;
				flex: 1;
				display: flex;
				justify-content: space-between;
				flex-direction: column;
				&::after {
					content: '';
					display: inline-block;
					background-color: #99d5ff;
					border-radius: 4px;
				}
				.row {
					height: 100%;
					background-color: #99d5ff;
					margin-bottom: 8px;
					border-radius: 4px;
					overflow: hidden;
					&:last-of-type {
						margin: 0;
					}
					&.assign {
						background-color: #99ceff;
						div {
							color: #4a70b9;
						}
					}
					&.free {
						background-color: #f5fffc;
						div {
							color: #00d18b;
						}
					}
					&.fault {
						background-color: #ffccd4;
						div {
							color: #e60050;
						}
					}
					&:last-of-type {
						border: none;
					}
					div {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 100%;
						height: 100%;
						font-size: 32px;
						color: #fff;
						&.active {
							background-color: #fff;
							color: #faa82d;
							border: 6px solid #ffc234;
							font-weight: bold;
							//   animation: borderColor 1s infinite  alternate;
						}
						// &.activeAnimation {
						//   animation: borderColor 1s infinite  alternate;
						// }
					}
					&.screen {
						display: flex;
						justify-content: center;
						align-items: flex-start;
						div {
							display: block;
							width: 100%;
							height: 100%;
							background: url('@{assets}/zg_icon.png') no-repeat;
							background-size: 100% auto;
							background-position: 0 0;
							border-bottom: 2px solid #77b8e6;
						}

						background-color: #99d5ff;
					}
					// &.active{
					//   background-color: #E6AC00;
					//   animation: borderColor 1s infinite  alternate;
					// }
				}
			}
		}
		.status {
			height: 72px;
			width: 100%;
			background-color: #fff;
			border-radius: 8px;
			flex-shrink: 0;
			display: flex;
			align-items: center;
			padding: 18px 24px;
			margin-top: 40px;
			.txt {
				color: #196aff;
				font-size: 24px;
			}
			.dot {
				font-size: 24px;
				display: flex;
				align-items: center;
				i {
					display: inline-block;
					width: 56px;
					height: 32px;
					border-radius: 8px;
					margin-left: 15px;
					margin-right: 12px;
				}
			}
			.kx {
				color: #73e5bf;
				i {
					background-color: #f5fffc;
					border: 2px solid #73e5bf;
				}
			}

			.zy {
				color: #5ca3e5;
				i {
					background-color: #99ceff;
					border: 2px solid #5ca3e5;
				}
			}

			.gz {
				color: #e57384;
				i {
					background-color: #ffccd4;
					border: 2px solid #e57384;
				}
			}
		}
	}
}

@keyframes borderColor {
	from {
		border: 4px solid #e6ac00;
	}

	to {
		border: 4px solid #cc8e0f;
	}
}
</style>
