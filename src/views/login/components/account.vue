<template>
	<div class="account-warp">
		<h2>账号密码认证</h2>
		<div class="form">
			<div class="form-item account">
				<input ref="account" v-model="account" type="text" :class="{ active: currentRef == 'account' }"
					placeholder="请输入用户名" @click="handleFocusRef('account')">
			</div>
			<div class="form-item password">
				<input ref="password" v-model="password" :class="{ active: currentRef == 'password' }" type="password"
					placeholder="请输入密码" @click="handleFocusRef('password')">
			</div>
			<p v-if="error" class="tip err">{{ error }}</p>
			<!-- <p v-else class="tip info">提示：初始密码为身份证后六位</p> -->
			<p v-else class="tip info"></p>
		</div>
		<keyboard @input="handleKeyIn" @delete="handleDelWord" class="keybox"></keyboard>
		<div class="login-warp">
			<span class="login-btn" @click="handleAccountLogin">立即认证</span>
		</div>
		<div class="switch-warp">
			<p class="tip">222认证出现问题？试试其他认证方式222</p>
			<i></i>
			<span class="switch-btn" @click="handleSwitchLogin"></span>
			<span class="switch-btn-text">人脸认证</span>
		</div>
		<div class="tool">
			<span class="back-btn" @click="handleBack">退出认证（{{ countdown }}s）</span>
		</div>
	</div>
</template>

<script>
import { SM3 } from 'gm-crypto'
import { setNewToken, debounce } from '@/libs/utils'
import store from '@/stroe'
import keyboard from '@/components/keyboard'
export default {
	props: {
		// 退出路由
		fromPath: {
			type: String,
			default: ''
		},
		// 认证后的去处路由
		toPath: {
			type: String,
			default: ''
		},
		handleGetRules: {
			type: Function,
			default: () => { }
		}
	},
	components: {
		keyboard
	},
	data() {
		return {
			currentRef: 'account',
			account: '',
			password: '',
			error: '',
			countdown: 120,
			timer: null,
			systemConfig: {}
		}
	},
	beforeDestroy() {
		this.timer && clearInterval(this.timer)
		this.timer = null
	},
	mounted() {
		this.systemConfig = store.state.config.terminalConfig
		// 获取所有人员
		console.log('synchronizePersonnel--face', '获取所有人员接口开始调用')
		this.getUserList()

		this.$refs[this.currentRef].focus()
		this.countdown = this.serverConfig.loginOutTime || 120
		this.countdownEvent()
	},
	methods: {
		getUserList() {
			console.log('synchronizePersonnel',this.$http.userApi.synchronizePersonnel, '调用获取所有人员人脸接口')
			this.$http.userApi.synchronizePersonnel().then((res) => {
				this.logger('请求成功', '调用获取所有人员人脸接口')
				this.$baseTip.success('操作成功,请稍后尝试')
			})
		},
		// 倒计时
		countdownEvent() {
			this.timer = setInterval(() => {
				this.countdown--
				if (this.countdown == 0) {
					clearInterval(this.timer)
					this.countdown = 0
					this.$router.push(this.fromPath)
				}
			}, 1000)
		},
		// 统筹保管员|暂存模式下的登录入口
		handleAccountLogin() {
			const loginMap = {
				0: this.handleLogin_bg,
				1: this.handleLogin
			}
			// 先判断物管柜配置使用的模式
			const patternTypeOfCabinet = this.serverConfig.patternType
			if (loginMap[patternTypeOfCabinet]) {
				loginMap[patternTypeOfCabinet]()
			} else {
				// 因5033版本兼容5011的保管服务，物管柜配置是没有返回patternType的，所以再从本地终端配置获取
				const patternTypeOfTerminal = this.systemConfig.patternType
				loginMap[patternTypeOfTerminal]()
			}
		},

		loginLoad() {
			const loginLoad = this.$GxxLoading({ txt: '正在登录中，请稍等...', isCloseBtn: true })
			return loginLoad
		},
		// 暂存模式密码登录
		handleLogin() {
			this.error = ''
			if (!this.account || !this.password) {
				this.error = '账号或密码不能为空！'
				return
			}
			const loginLoad = this.loginLoad()
			const passwordVal = SM3.digest(this.password, 'utf8', 'base64')
			// 登录之前新增身份校验
			const params = {
				username: this.account,
				password: passwordVal,
				client_id: 'user_client',
				client_secret: 'user_client',
				grant_type: 'password',
				app_mark: 'sacw',
				scope: 'trust',
				auth_type: 'cy_gm'
			}
			this.$Post_SH(this.BspApi.login_url, params)
				.then((res) => {
					if (res.code === 400) {
						this.error = res.msg
						return
					}
					this.$store.commit('setUser', res)
					setNewToken(res.access_token)
					window.frist_in = 0
					this.handleGetRules()
					this.$router.push(this.toPath)
				})
				.finally(() => {
					loginLoad.close()
				})
		},
		// 保管模式密码登录
		handleLogin_bg() {
			const loginLoad = this.loginLoad()
			// 登录之前新增身份校验
			const params = {
				cabinetCode: this.serverConfig.cabinetCode,
				centerId: this.serverConfig.centerId,
				type: '01',
				loginId: this.account,
				pwd: this.password
			}
			this.$Post(this.PymApi.getAdminInfo, params)
				.then((res) => {
					if (res.success) {
						this.$store.commit('setUser', res)
						localStorage.setItem('userInfo', JSON.stringify(res.admin))
						this.$router.push(this.toPath)
					} else {
						this.ModalInfo('确认', 'tipsIcon2', res.msg)
					}
				})
				.finally(() => {
					loginLoad.close()
				})
		},

		handleSwitchLogin() {
			this.$emit('switchLogin', 'face')
		},
		handleFocusRef(ref_name) {
			this.currentRef = ref_name
		},
		handleKeyIn(word) {
			this[this.currentRef] = this[this.currentRef] += String(word)

			debounce(() => {
				this.$refs[this.currentRef].focus()
			}, 500)
		},
		handleDelWord() {
			if (!this[this.currentRef]) return
			this[this.currentRef] = this[this.currentRef].substring(0, this[this.currentRef].length - 1)

			debounce(() => {
				this.$refs[this.currentRef].focus()
			}, 500)
		},
		handleBack() {
			this.$router.push(this.fromPath)
		}
	}
}
</script>

<style lang="less" scoped>
@assets: '~@/assets/images/temp';

.account-warp {
	position: fixed;
	width: 100%;
	height: 100%;
	background: rgba(24, 22, 67, 0.9);
	top: 0;
	left: 0;
	overflow: auto;

	h2 {
		color: #fff;
		font-size: 48px;
		text-align: center;
		margin-top: 116px;
		margin-bottom: 70px;
	}

	.form {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-bottom: 10px;

		.form-item {
			width: 760px;
			height: 120px;
			margin-bottom: 40px;

			&.password {
				margin: 0;
			}

			input {
				width: 100%;
				height: 100%;
				border-radius: 16px;
				font-size: 40px;
				padding: 0 40px;
				color: #fff;
				border: 3px solid #669eff;
				background-color: transparent;

				&::placeholder {
					color: #669eff;
				}

				&:focus {
					outline: none;
				}

				&:focus-visible {
					background-color: rgba(102, 255, 255, 0.1);
				}

				&.active {
					border-color: #66ffff;
					background-color: rgba(102, 255, 255, 0.1);
				}
			}
		}

		.tip {
			height: 60px;
			font-size: 28px;
			line-height: 1;
			width: 760px;
			padding-left: 44px;
			display: flex;
			align-items: center;

			&.info {
				color: #fff;
			}

			&.err {
				color: #cc2936;
			}
		}
	}

	.keyword-num-warp {
		width: 760px;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin: 0 auto;

		.row {
			width: 100%;
			display: flex;
			justify-content: space-between;
			margin-bottom: 24px;

			span {
				display: inline-flex;
				width: 240px;
				height: 120px;
				align-items: center;
				justify-content: center;
				font-size: 72px;
				color: #669eff;
				border: 3px solid #669eff;
				border-radius: 16px;
				cursor: pointer;

				&:active {
					background-color: #669eff;
					border-color: #669eff;
					color: #fff;

					i {
						color: #fff !important;
					}
				}

				&.small {
					width: 170px;
				}

				&.switch {
					i {
						font-size: 32px;
						color: #2e71e5;
						margin-top: 20px;

						&.active {
							font-size: 48px;
							color: #669eff;
							margin-top: -20px;
						}
					}
				}

				&.backspace {
					display: flex;
					justify-content: center;
					align-items: center;

					i {
						display: inline-block;
						width: 66px;
						height: 40px;
						background-size: 100% 100%;
						background-image: url('@{assets}/backspace-icon.png');
					}
				}
			}
		}
	}

	.keyword-en-warp {
		width: 984px;
		margin: 0 auto;
		display: flex;
		flex-direction: column;

		.row {
			width: 100%;
			margin-bottom: 24px;
			display: flex;
			justify-content: space-between;

			span {
				display: inline-flex;
				width: 100px;
				height: 120px;
				align-items: center;
				justify-content: center;
				color: #669eff;
				font-size: 72px;
				border: 2px solid #669eff;
				border-radius: 16px;
				flex-shrink: 0;

				&:active {
					background-color: #669eff;
					border-color: #669eff;
					color: #fff;

					i {
						color: #fff !important;
					}
				}

				&.switch {
					width: 228px;

					i {
						font-size: 32px;
						color: #2e71e5;
						margin-top: 20px;

						&.active {
							font-size: 48px;
							color: #669eff;
							margin-top: -20px;
						}
					}
				}

				&.EN_en {
					width: 228px;
					display: flex;
					justify-content: center;
					align-items: center;

					i {
						display: inline-block;
						width: 58px;
						height: 68px;
						background-size: 100% 100%;
						background-image: url('@{assets}/switch-en.png');
					}
				}

				&.backspace {
					width: 228px;
					display: flex;
					justify-content: center;
					align-items: center;

					i {
						display: inline-block;
						width: 66px;
						height: 40px;
						background-size: 100% 100%;
						background-image: url('@{assets}/backspace-icon.png');
					}
				}
			}
		}
	}

	.login-warp {
		display: flex;
		justify-content: center;
		margin-top: 16px;

		.login-btn {
			width: 760px;
			height: 120px;
			border-radius: 16px;
			background-color: #337eff;
			color: #fff;
			font-size: 48px;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0px 12px 20px 1px rgba(51, 126, 255, 0.3);
			cursor: pointer;
		}
	}

	.switch-warp {
		display: flex;
		flex-direction: column;
		align-items: center;

		.tip {
			color: #669eff;
			font-size: 28px;
			margin-top: 44px;
			line-height: 1;
		}

		i {
			display: inline-block;
			width: 32px;
			height: 30px;
			// background-color: #669EFF;
			margin-top: 18px;
			background-size: 100% 100%;
			background-image: url('@{assets}/arrow-down.png');
		}

		.switch-btn {
			display: inline-block;
			width: 128px;
			height: 128px;
			background-size: 100% 100%;
			background-image: url('@{assets}/switch-face-icon.png');
			margin-top: 25px;
		}

		.switch-btn-text {
			font-size: 28px;
			color: #669eff;
			margin-top: 15px;
		}
	}

	.tool {
		display: flex;
		justify-content: center;
		margin-top: 65px;
		padding-bottom: 100px;

		.back-btn {
			width: 340px;
			height: 96px;
			border-radius: 8px;
			background-color: #b24750;
			color: #fff;
			font-size: 36px;
			display: flex;
			align-items: center;
			justify-content: center;

			&:active {
				transform: scale(0.98);
			}
		}
	}
}

.keybox {
	padding: 59px 8px 86px;
}
</style>
