import Vue from 'vue'
import store from './stroe'
import App from './App.vue'
import router from './router'
import iView from 'view-design'
import VueRouter from 'vue-router'
import '@/assets/js/jquery.min.js'
import GosuncnUI from 'gosuncn-ui'
import 'gosuncn-ui/lib/style/index.css'
import Vconsole from 'vconsole'

import {
	$Post,
	$Get,
	$Post_SH,
	$Upload
} from './api/fetch'
import http from './api/httpApi'
import API from './api/index'
import {
	logger
} from '@/libs/log'
import {
	modalInfo,
	modalConfirm
} from '@/libs/modal'
import './directive/index'
import 'babel-polyfill'

Vue.use(iView)
Vue.use(GosuncnUI)
Vue.use(VueRouter)
let vConsole = new Vconsole()
Vue.use(vConsole)
Vue.prototype.$EventBus = new Vue()
Vue.prototype.BspApi = API.BspApi
Vue.prototype.FaceApi = API.FaceApi
Vue.prototype.UmtApi = API.UmtApi
Vue.prototype.PymApi = API.PymApi
Vue.prototype.UploadApi = API.UploadApi
Vue.prototype.logger = logger
Vue.prototype.$store = store


function updateApiConfig() {
	Vue.prototype.$http = http
	Vue.prototype.$Post = $Post
	Vue.prototype.$Get = $Get
	Vue.prototype.$Post_SH = $Post_SH
	Vue.prototype.$Upload = $Upload
}
updateApiConfig()

window.updateLocalConfig = function () {
	const serverConfig = JSON.parse(localStorage.getItem('serverConfig'))
	Vue.prototype.serverConfig = serverConfig
	updateApiConfig()
}

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
	return originalPush.call(this, location).catch((err) => err)
}

Vue.prototype.ModalInfo = modalInfo
Vue.prototype.ModalConfirm = modalConfirm

Vue.prototype.env = process.env.NODE_ENV !== 'development' ? 'pro' : 'dev' // test，pro，dev 测试，生产，开发模式（开关柜测试模式切换）

Vue.config.productionTip = false
new Vue({
	store,
	router,
	render: (h) => h(App)
}).$mount('#app')
